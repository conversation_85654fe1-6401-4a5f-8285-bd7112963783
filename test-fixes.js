// Test script to verify the sync fixes
// Run this in the browser console after logging in

async function testSyncFixes() {
  console.log("🧪 Testing sync fixes...");
  
  try {
    // Test 1: Check if sync status records exist for all tables
    console.log("\n1️⃣ Testing sync status records...");
    
    const { syncService } = await import('./lib/sync.js');
    const { db } = await import('./lib/db.js');
    
    // Test getting sync status for all tables
    const allSyncStatus = await syncService.getSyncStatus();
    console.log(`✅ Retrieved sync status for ${allSyncStatus.length} tables`);
    
    // Test getting sync status for specific table (patients)
    const patientsStatus = await syncService.getSyncStatusForTable('patients');
    if (patientsStatus) {
      console.log(`✅ Patients sync status found: ${JSON.stringify(patientsStatus)}`);
    } else {
      console.log("❌ Patients sync status not found");
    }
    
    // Test 2: Check if sync can run without race condition
    console.log("\n2️⃣ Testing sync race condition fix...");
    
    // Try to trigger multiple syncs
    const syncPromises = [
      syncService.syncAll(),
      syncService.syncAll(),
      syncService.syncAll()
    ];
    
    const results = await Promise.allSettled(syncPromises);
    
    let successCount = 0;
    let raceConditionErrors = 0;
    
    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        successCount++;
        console.log(`✅ Sync ${index + 1} completed successfully`);
      } else {
        if (result.reason.message === 'Sync already in progress') {
          raceConditionErrors++;
          console.log(`⚠️ Sync ${index + 1} properly rejected: ${result.reason.message}`);
        } else {
          console.log(`❌ Sync ${index + 1} failed with unexpected error: ${result.reason.message}`);
        }
      }
    });
    
    console.log(`\n📊 Results: ${successCount} successful, ${raceConditionErrors} properly rejected`);
    
    if (successCount >= 1 && raceConditionErrors >= 0) {
      console.log("✅ Race condition handling is working correctly");
    }
    
    // Test 3: Verify sync status records exist for all expected tables
    console.log("\n3️⃣ Testing all expected sync status records...");
    
    const expectedTables = [
      'patients', 'vital_signs', 'lab_values', 'medications', 
      'medication_names', 'medication_adherence', 'doctor_notes', 
      'cultures', 'radiology', 'unit_types'
    ];
    
    let missingTables = [];
    
    for (const tableName of expectedTables) {
      const status = await syncService.getSyncStatusForTable(tableName);
      if (status) {
        console.log(`✅ ${tableName}: sync status exists`);
      } else {
        console.log(`❌ ${tableName}: sync status missing`);
        missingTables.push(tableName);
      }
    }
    
    if (missingTables.length === 0) {
      console.log("✅ All expected sync status records exist");
    } else {
      console.log(`❌ Missing sync status for: ${missingTables.join(', ')}`);
    }
    
    console.log("\n🎉 Sync fixes test completed!");
    
  } catch (error) {
    console.error("❌ Test failed:", error);
  }
}

// Auto-run the test
testSyncFixes();
