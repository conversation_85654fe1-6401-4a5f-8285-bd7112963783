"use client"

import <PERSON><PERSON>, { type Table } from "dexie"
import type {
  Patient,
  VitalSigns,
  LabValues,
  Medication,
  MedicationName,
  DoctorNote,
  Culture,
  Radiology,
  OutboxOperation,
  IdMapping,
  UnitType,
  SyncStatus,
} from "./types"

export class ICUDatabase extends Dexie {
  // Reference tables (only unit_types remains)
  unit_types!: Table<UnitType>
  
  // Main tables
  patients!: Table<Patient>
  vital_signs!: Table<VitalSigns>
  lab_values!: Table<LabValues>
  medications!: Table<Medication>
  medication_names!: Table<MedicationName>
  medication_adherence!: Table<import("./types").MedicationAdherence>
  doctor_notes!: Table<DoctorNote>
  cultures!: Table<Culture>
  radiology!: Table<Radiology>
  outbox!: Table<OutboxOperation>
  id_mappings!: Table<IdMapping>
  sync_status!: Table<SyncStatus>

  constructor() {
    super("ICUDatabase")
    // Base schema
    this.version(1).stores({
      patients: "id, patient_id, name, admission_date, created_at",
      vital_signs: "id, patient_id, date, created_at, [patient_id+date]",
      lab_values: "id, patient_id, date, created_at, [patient_id+date]",
      medications: "id, patient_id, medication_name, date_prescribed, is_active",
      medication_names: "id, name",
      doctor_notes: "id, patient_id, date, created_at",
      cultures: "id, patient_id, requested_date, status, microorganism",
      radiology: "id, patient_id, scan_date, scan_type, status",
      outbox: "id, table_name, operation, created_at, retries",
      id_mappings: "local_id, server_id, table_name",
    })

    // Add medication adherence in a new version to handle upgrades
    this.version(2).stores({
      medication_adherence:
        "id, patient_id, medication_id, date, is_taking_medication, [patient_id+medication_id+date]",
    })

    // Add vital signs JSONB structure in version 3
    this.version(3).stores({
      vital_signs: "id, patient_id, date, vital_signs_data, created_at, [patient_id+date]",
    })

    // Version 6: Remove lookup tables, use direct string values
    this.version(6).stores({
      // Only unit_types remains as reference table
      unit_types: "id, name",
      
      // Update main tables to use direct string values
      patients: "id, patient_id, name, gender, unit_id, admission_date, created_at",
      cultures: "id, patient_id, requested_date, status, microorganism",
      radiology: "id, patient_id, scan_date, scan_type, status",
    }).upgrade(async (tx) => {
      // Seed unit_types from online database on first load
      try {
        // Import supabase dynamically to avoid circular dependencies
        const { supabase } = await import('./supabase')
        
        const { data: onlineUnits, error } = await supabase
          .from('unit_types')
          .select('id, name')
          .order('name')

        if (!error && onlineUnits && onlineUnits.length > 0) {
          // Transform online data to match UnitType interface
          const now = new Date().toISOString()
          const transformedUnits = onlineUnits.map(unit => ({
            ...unit,
            is_active: true,
            created_at: now
          }))
          await tx.table('unit_types').bulkAdd(transformedUnits)
        } else {
          // Fallback to hardcoded units if online fetch fails
          console.log('Online fetch failed or returned no data, using fallback units')
          const now = new Date().toISOString()
          await tx.table('unit_types').bulkAdd([
            { id: 1, name: 'ICU A', is_active: true, created_at: now },
            { id: 2, name: 'ICU B', is_active: true, created_at: now }
          ])
        }
      } catch (error) {
        console.error('Error fetching units from online DB during upgrade:', error)
        // Fallback to hardcoded units
        const now = new Date().toISOString()
        await tx.table('unit_types').bulkAdd([
          { id: 1, name: 'ICU A', is_active: true, created_at: now },
          { id: 2, name: 'ICU B', is_active: true, created_at: now }
        ])
      }
    })

    // Version 7: Add standardized sync columns (updated_at, dirty, deleted) to all tables
    this.version(7).stores({
      // Keep existing table definitions and add sync columns
      unit_types: "id, name, updated_at, dirty, deleted",
      patients: "id, patient_id, name, gender, unit_id, admission_date, created_at, updated_at, dirty, deleted",
      vital_signs: "id, patient_id, date, vital_signs_data, created_at, updated_at, dirty, deleted, [patient_id+date]",
      lab_values: "id, patient_id, date, lab_data, created_at, updated_at, dirty, deleted, [patient_id+date]",
      medications: "id, patient_id, medication_name, date_prescribed, is_active, created_at, updated_at, dirty, deleted",
      medication_names: "id, name, created_at, updated_at, dirty, deleted",
      medication_adherence: "id, patient_id, medication_id, date, is_taking_medication, created_at, updated_at, dirty, deleted, [patient_id+medication_id+date]",
      doctor_notes: "id, patient_id, date, created_at, updated_at, dirty, deleted",
      cultures: "id, patient_id, requested_date, status, microorganism, created_at, updated_at, dirty, deleted",
      radiology: "id, patient_id, scan_date, scan_type, status, created_at, updated_at, dirty, deleted",
      sync_status: "tableName, lastSyncAt, isSyncing, error",
      // Keep outbox and id_mappings for backward compatibility during transition
      outbox: "id, table_name, operation, created_at, retries",
      id_mappings: "local_id, server_id, table_name",
    }).upgrade(async (tx) => {
      try {
        // Add default values for new columns to existing records
        const now = new Date().toISOString()

        // Update all existing records with default sync column values
        const tables = ['unit_types', 'patients', 'vital_signs', 'lab_values', 'medications',
                       'medication_names', 'medication_adherence', 'doctor_notes', 'cultures', 'radiology']

        for (const tableName of tables) {
          try {
            const table = tx.table(tableName)
            const records = await table.toArray()

            for (const record of records) {
              // Only update if the new columns don't already exist
              if (record.updated_at === undefined || record.dirty === undefined || record.deleted === undefined) {
                await table.update(record.id, {
                  updated_at: record.created_at || now,
                  dirty: false, // Existing records are assumed to be synced
                  deleted: false
                })
              }
            }
            console.log(`✅ Migrated ${records.length} records in ${tableName}`)
          } catch (error) {
            console.error(`❌ Failed to migrate table ${tableName}:`, error)
            // Continue with other tables even if one fails
          }
        }

        // Initialize sync status for all tables
        try {
          const syncStatusTable = tx.table('sync_status')
          const syncConfigs = [
            'unit_types', 'patients', 'vital_signs', 'lab_values',
            'medications', 'medication_names', 'medication_adherence',
            'doctor_notes', 'cultures', 'radiology'
          ]

          for (const tableName of syncConfigs) {
            try {
              await syncStatusTable.add({
                tableName,
                lastSyncAt: null,
                isSyncing: false,
                error: null
              })
            } catch (error) {
              // Ignore if already exists
              console.log(`Sync status for ${tableName} already exists`)
            }
          }
          console.log('✅ Sync status initialized')
        } catch (error) {
          console.error('❌ Failed to initialize sync status:', error)
        }

        console.log('🎉 Database migration to version 7 completed successfully')
      } catch (error) {
        console.error('💥 Database migration failed:', error)
        throw error
      }
    })
  }

  // Helper methods for reference data (only unit_types remains)
  async getUnitTypes() {
    return await this.unit_types.filter(record => !record.deleted).toArray()
  }

  // Sync helper methods
  async getSyncStatus(tableName: string): Promise<SyncStatus | undefined> {
    return await this.sync_status.get(tableName)
  }

  async updateSyncStatus(tableName: string, updates: Partial<SyncStatus>) {
    await this.sync_status.update(tableName, updates)
  }

  /**
   * Ensure sync status record exists for a table, create if missing
   */
  async ensureSyncStatus(tableName: string): Promise<void> {
    try {
      const existing = await this.sync_status.get(tableName)
      if (!existing) {
        await this.sync_status.add({
          tableName,
          lastSyncAt: null,
          isSyncing: false,
          error: null
        })
        console.log(`✅ Created missing sync status for table: ${tableName}`)
      }
    } catch (error) {
      console.error(`❌ Failed to ensure sync status for ${tableName}:`, error)
      throw error
    }
  }

  /**
   * Ensure sync status records exist for all configured tables
   */
  async ensureAllSyncStatus(): Promise<void> {
    const syncConfigs = [
      'unit_types', 'patients', 'vital_signs', 'lab_values',
      'medications', 'medication_names', 'medication_adherence',
      'doctor_notes', 'cultures', 'radiology'
    ]

    for (const tableName of syncConfigs) {
      await this.ensureSyncStatus(tableName)
    }
  }

  async getDirtyRecords(tableName: string) {
    // @ts-ignore - Dynamic table access
    return await this[tableName].filter(record => record.dirty === true && record.deleted !== true).toArray()
  }

  async getDeletedRecords(tableName: string) {
    // @ts-ignore - Dynamic table access
    return await this[tableName].filter(record => record.deleted === true).toArray()
  }

  async markRecordClean(tableName: string, id: string) {
    // @ts-ignore - Dynamic table access
    await this[tableName].update(id, { dirty: false })
  }

  async markRecordDirty(tableName: string, id: string) {
    const now = new Date().toISOString()
    // @ts-ignore - Dynamic table access
    await this[tableName].update(id, { dirty: true, updated_at: now })
  }

  async softDeleteRecord(tableName: string, id: string) {
    const now = new Date().toISOString()
    // @ts-ignore - Dynamic table access
    await this[tableName].update(id, { deleted: true, dirty: true, updated_at: now })
  }

  async permanentlyDeleteRecord(tableName: string, id: string) {
    // @ts-ignore - Dynamic table access
    await this[tableName].delete(id)
  }

  async getRecordsUpdatedAfter(tableName: string, timestamp: string) {
    // @ts-ignore - Dynamic table access
    return await this[tableName].filter(record =>
      record.updated_at > timestamp && record.deleted !== true
    ).toArray()
  }
}

export const db = new ICUDatabase()
