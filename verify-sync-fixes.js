// Quick verification script for sync fixes
// Run this in Node.js to verify the fixes are in place

const fs = require('fs');
const path = require('path');

function checkFileContains(filePath, searchText, description) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const found = content.includes(searchText);
    console.log(`${found ? '✅' : '❌'} ${description}`);
    return found;
  } catch (error) {
    console.log(`❌ Error reading ${filePath}: ${error.message}`);
    return false;
  }
}

function checkFileNotContains(filePath, searchText, description) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const found = content.includes(searchText);
    console.log(`${!found ? '✅' : '❌'} ${description}`);
    return !found;
  } catch (error) {
    console.log(`❌ Error reading ${filePath}: ${error.message}`);
    return false;
  }
}

console.log('🔍 Verifying sync fixes...\n');

// Check Fix 1: Race condition fix
console.log('1️⃣ Checking race condition fix:');
checkFileContains(
  'lib/sync-engine.ts',
  'Don\'t trigger immediate sync to avoid race conditions',
  'Added comment explaining the fix'
);

// Check that the immediate sync trigger was removed from startPeriodicSync
const syncEngineContent = fs.readFileSync('lib/sync-engine.ts', 'utf8');
const startPeriodicSyncMatch = syncEngineContent.match(/startPeriodicSync\(\)\s*{[^}]*}/s);
if (startPeriodicSyncMatch) {
  const methodContent = startPeriodicSyncMatch[0];
  const hasImmediateTrigger = methodContent.includes('this.triggerSync()') &&
                              !methodContent.includes('setInterval');
  console.log(`${!hasImmediateTrigger ? '✅' : '❌'} Removed immediate sync trigger from startPeriodicSync()`);
} else {
  console.log('❌ Could not find startPeriodicSync method');
}

// Check Fix 2: Sync status record creation
console.log('\n2️⃣ Checking sync status fix:');
checkFileContains(
  'lib/db.ts',
  'async ensureSyncStatus(tableName: string)',
  'Added ensureSyncStatus method'
);

checkFileContains(
  'lib/db.ts',
  'async ensureAllSyncStatus()',
  'Added ensureAllSyncStatus method'
);

checkFileContains(
  'lib/sync.ts',
  'await db.ensureAllSyncStatus()',
  'Updated getSyncStatus to ensure records exist'
);

checkFileContains(
  'lib/sync.ts',
  'await db.ensureSyncStatus(tableName)',
  'Updated getSyncStatusForTable to ensure record exists'
);

checkFileContains(
  'lib/sync-engine.ts',
  'await db.ensureSyncStatus(tableName)',
  'Updated sync engine to ensure sync status exists'
);

console.log('\n🎉 Verification complete!');
console.log('\nTo test the fixes:');
console.log('1. Run: npm run dev');
console.log('2. Check console for no "Sync already in progress" errors');
console.log('3. Navigate to: http://localhost:3000/sync-test');
console.log('4. Click "Run Full Tests" and verify all tests pass');
